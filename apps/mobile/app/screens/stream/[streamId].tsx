import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,

  SafeAreaView,
  Animated,
  PanResponder,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Dimensions,

  FlatList,
  ActivityIndicator,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useUser } from "../../../hooks/useUser";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import StreamSkeleton from "../../../components/skeletons/StreamSkeleton";
import UserAvatar from "../../../components/ui/user-avatar";
import { getImageUrl } from "apps/mobile/lib/utils";
// import {
//   LiveKitRoom,
//   useTracks,
//   VideoTrack,
//   AudioSession,
//   isTrackReference,
// } from "@livekit/react-native";
// import { Track } from "livekit-client";

const LIVEKIT_WS_URL = process.env.EXPO_PUBLIC_LIVEKIT_URL;

const StreamScreen = () => {
  const params = useLocalSearchParams<{ streamId: string }>();
  const streamId = params.streamId;
  const { user } = useUser();
  const router = useRouter();
  const [message, setMessage] = useState("");

  const chatAreaRef = React.useRef<View>(null);
  const [uiVisible, setUiVisible] = useState(true);
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  const overlayOpacity = React.useRef(new Animated.Value(0.5)).current;
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(false);
  const [isTouchingChatArea, setIsTouchingChatArea] = useState(false);
  const [starting, setStarting] = useState(false);


  const [livekitToken, setLivekitToken] = useState<string | null>(null);
  const [isHost, setIsHost] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [livekitError, setLivekitError] = useState<string | null>(null);
  const [ingestInfo, setIngestInfo] = useState<{ ingestUrl: string; streamKey: string } | null>(null);

  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [optimisticBookmarked, setOptimisticBookmarked] = useState<boolean | null>(null);
  const startStream = useMutation(api.streams.goLive);
  const sendMessage = useMutation(api.streams.sendMessage);
  const generateHostToken = useAction(api.integration.livekit.generateHostToken);
  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);
  const generateIngestInfo = useAction(api.integration.livekit.generateIngestInfo);

  const stream = useQuery(
    api.streams.getStream,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  const chatMessages = useQuery(
    api.streams.listMessages,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  // Determine if user is host
  useEffect(() => {
    if (user && stream) {
      setIsHost(stream.hostId === user._id);
    }
  }, [user, stream]);

  // Fetch LiveKit token
  useEffect(() => {
    if (!streamId || !user || !stream) return;
    setLivekitToken(null);
    setLivekitError(null);
    setConnecting(true);
    if (stream.status === "live" || (isHost && stream.status !== "ended")) {
      const fn = isHost ? generateHostToken : generateViewerToken;
      fn({ streamId: streamId as Id<"streams"> })
        .then((token) => {
          setLivekitToken(token);
          setConnecting(false);
        })
        .catch((err) => {
          setLivekitError("Failed to get LiveKit token: " + (err?.message || err));
          setConnecting(false);
        });
    } else {
      setConnecting(false);
    }
  }, [streamId, user, stream, isHost]);

  // Fetch ingest info for OBS if host
  useEffect(() => {
    if (isHost && stream && !ingestInfo && stream.status !== "live") {
      generateIngestInfo({ streamId: streamId as Id<"streams"> })
        .then(setIngestInfo)
        .catch(() => {});
    }
  }, [isHost, stream, ingestInfo, streamId, generateIngestInfo]);

  // useEffect(() => {
  //   if (!livekitToken) return;
  //   AudioSession.startAudioSession();
  //   return () => {
  //     AudioSession.stopAudioSession();
  //   };
  // }, [livekitToken]);



  // PanResponder and UI logic (unchanged)
  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => {
        if (chatAreaRef.current) {
          return false;
        }
        return true;
      },
      onMoveShouldSetPanResponder: (_, gestureState) => {
        if (
          isTouchingChatArea &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx)
        ) {
          return false;
        }
        return (
          Math.abs(gestureState.dx) > 20 &&
          Math.abs(gestureState.dx) > Math.abs(gestureState.dy)
        );
      },
      onPanResponderTerminationRequest: () => true,
      onPanResponderGrant: () => {},
      onPanResponderMove: () => {},
      onPanResponderRelease: (_, gestureState) => {
        if (Math.abs(gestureState.dx) < 5 && Math.abs(gestureState.dy) < 5) {
          if (!uiVisible) {
            setUiVisible(true);
            setShowSwipeIndicator(false);
            Animated.parallel([
              Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
              }),
              Animated.timing(overlayOpacity, {
                toValue: 0.5,
                duration: 200,
                useNativeDriver: false,
              }),
            ]).start();
          }
          return true;
        } else if (gestureState.dx < -40 && uiVisible) {
          setUiVisible(false);
          setShowSwipeIndicator(true);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.2,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        } else if (gestureState.dx > 40) {
          setUiVisible(true);
          setShowSwipeIndicator(false);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.5,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }
        return true;
      },
      onPanResponderTerminate: () => true,
    }),
  ).current;

  // Chat and other UI logic (unchanged)
  const handleToggleBookmark = async () => {
    if (!streamId) return;
    setOptimisticBookmarked((prev) => !(prev ?? false));
    try {
      const result = await toggleBookmark({
        streamId: streamId as Id<"streams">,
      });
      setOptimisticBookmarked(result.bookmarked);
    } catch (error) {
      console.error("Error toggling bookmark:", error);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !streamId || !user) return;
    try {
      await sendMessage({
        streamId: streamId as Id<"streams">,
        text: message.trim(),
      });
      setMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  // Video rendering using LiveKitRoom and VideoTrack
  // const VideoArea = useCallback(() => {
  //   if (!livekitToken || !LIVEKIT_WS_URL || !stream?.roomName) {
  //     return (
  //       <View style={{ width: '100%', height: 300, backgroundColor: '#000', alignItems: 'center', justifyContent: 'center' }}>
  //         <Text style={{ color: '#fff' }}>Waiting for stream...</Text>
  //       </View>
  //     );
  //   }
  //   return (
  //     <LiveKitRoom
  //       serverUrl={LIVEKIT_WS_URL}
  //       token={livekitToken}
  //       connect={true}
  //       audio={true}
  //       video={true}
  //       options={{ adaptiveStream: { pixelDensity: 'screen' } }}
  //     >
  //       <RoomView />
  //     </LiveKitRoom>
  //   );
  // }, [livekitToken, LIVEKIT_WS_URL, stream?.roomName, isHost, user?._id]);


  // RoomView for rendering video tracks
  // const RoomView = () => {
  //   const tracks = useTracks([Track.Source.Camera]);
  //   // Host: show own camera; Viewer: show host's camera
  //   return (
  //     <View style={{ flex: 1 }}>
  //       <FlatList
  //         data={tracks}
  //         renderItem={({ item }) =>
  //           isTrackReference(item) ? (
  //             <VideoTrack trackRef={item} style={{ height: 300 }} />
  //           ) : (
  //             <View style={{ height: 300, backgroundColor: '#000' }} />
  //           )
  //         }
  //         keyExtractor={(item) => (isTrackReference(item) ? item.sid : String(item))}
  //       />
  //     </View>
  //   );
  // };

  if (!streamId) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            No stream ID provided.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  if (stream === undefined) {
    return <StreamSkeleton />;
  }
  if (!stream) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            Stream not found.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const logoSource = stream.thumbnail
    ? { uri: stream.thumbnail }
    : require("@workspace/assets/images/icon.png");

  const isModerator = (userId: Id<"users">) => {
    return stream?.moderatorIds?.includes(userId) || false;
  };

  const sortedMessages = chatMessages
    ? [...chatMessages].sort((a, b) => b._creationTime - a._creationTime)
    : [];

  const filteredMessages = [];
  const seenJoinedUsers = new Set();
  for (const msg of sortedMessages) {
    if (msg.type === "system" && msg.user?._id) {
      if (seenJoinedUsers.has(msg.user._id)) {
        continue;
      }
      seenJoinedUsers.add(msg.user._id);
    }
    filteredMessages.push(msg);
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <View style={styles.container}>
          {stream?.status === "live" ? (
            <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
              {connecting ? (
                <ActivityIndicator size="large" color="#fff" style={{ marginTop: 100 }} />
              ) : livekitError ? (
                <Text style={{ color: '#fff', textAlign: 'center', marginTop: 100 }}>{livekitError}</Text>
              ) : (
                <VideoArea />
              )}
            </View>
          ) : (
            <Image
              source={logoSource}
              style={[styles.backgroundImage, StyleSheet.absoluteFill]}
            />
          )}

          <Animated.View
            style={[styles.overlayGradient, { opacity: overlayOpacity }]}
          />

          {stream?.status !== "live" &&
            user &&
            (isHost ? (
              <View
                style={{
                  position: "absolute",
                  top: 150,
                  left: 0,
                  right: 0,
                  alignItems: "center",
                  zIndex: 10,
                }}
              >
                {ingestInfo && (
                  <View style={{ backgroundColor: '#222', borderRadius: 12, padding: 16, margin: 16 }}>
                    <Text style={{ color: '#fff', fontWeight: 'bold', marginBottom: 8 }}>OBS/RTMP Streaming Info</Text>
                    <Text style={{ color: '#fff' }}>Ingest URL: {ingestInfo.ingestUrl}</Text>
                    <Text style={{ color: '#fff' }}>Stream Key: {ingestInfo.streamKey}</Text>
                  </View>
                )}
                <TouchableOpacity
                  style={styles.startStreamButton}
                  onPress={async () => {
                    setStarting(true);
                    try {
                      await startStream({ streamId: streamId as Id<'streams'> });
                    } catch (e) {
                      // handle error
                    }
                    setStarting(false);
                  }}
                  disabled={starting}
                >
                  <Text style={styles.startStreamButtonText}>{starting ? 'Starting...' : 'Start Stream'}</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View
                style={{
                  position: "absolute",
                  top: 150,
                  left: 0,
                  right: 0,
                  alignItems: "center",
                  zIndex: 10,
                }}
              >
                <View
                  style={{
                    backgroundColor: "rgba(34,34,34,0.95)",
                    borderRadius: 30,
                    padding: 24,
                    margin: 16,
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: "#3A3A3C",
                    width: "90%",
                  }}
                >
                  <Text
                    style={{
                      color: "#8E8E93",
                      fontSize: 16,
                      marginBottom: 16,
                      textAlign: "center",
                    }}
                  >
                    Show Starts at{" "}
                    {stream && stream.scheduledTime
                      ? new Date(stream.scheduledTime).toLocaleTimeString(
                          [],
                          { hour: "2-digit", minute: "2-digit" },
                        )
                      : ""}
                  </Text>
                  <TouchableOpacity
                    style={{
                      backgroundColor: "#1a96da",
                      borderRadius: 30,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      paddingHorizontal: 32,
                      paddingVertical: 16,
                      width: "100%",
                      marginBottom: 16,
                    }}
                    onPress={handleToggleBookmark}
                  >
                    <Ionicons
                      name={
                        optimisticBookmarked ? "bookmark" : "bookmark-outline"
                      }
                      size={20}
                      color="#fff"
                    />
                    <Text
                      style={{
                        color: "#fff",
                        fontWeight: "700",
                        fontSize: 18,
                        marginLeft: 8,
                      }}
                    >
                      {optimisticBookmarked
                        ? "Bookmarked"
                        : "Bookmark & Notify Me"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}

          <SafeAreaView style={styles.safeArea}>
            <Animated.View style={[styles.topSection, { opacity: fadeAnim }]}>
              <View style={styles.topLeftRow}>
                <TouchableOpacity
                  onPress={() => router.back()}
                  style={styles.backButton}
                >
                  <Ionicons
                    name="chevron-back-outline"
                    size={24}
                    color="#fff"
                  />
                </TouchableOpacity>
                {stream && stream.hostId && (
                  <TouchableOpacity
                    onPress={() =>
                      stream.hostId &&
                      router.push({
                        pathname: "/(tabs)/user/[username]",
                        params: { username: stream.streamer?.username || "" },
                      })
                    }
                    style={styles.userAvatarContainer}
                  >
                    <UserAvatar
                      image={getImageUrl(stream.streamer?.image)}
                      size={32}
                      username={
                        stream.streamer?.username || stream.streamer?.name || "User"
                      }
                    />
                  </TouchableOpacity>
                )}
                <Text style={styles.topUsername} numberOfLines={1}>
                  {stream
                    ? stream.streamer?.username || stream.streamer?.name || "User"
                    : ""}
                </Text>
              </View>
            </Animated.View>

            <View style={styles.gestureContainer} {...panResponder.panHandlers}>


              <Animated.View
                style={[styles.contentContainer, { opacity: fadeAnim }]}
              >
                <View
                  style={styles.chatMainContainer}
                  ref={chatAreaRef}
                  onTouchStart={() => setIsTouchingChatArea(true)}
                  onTouchEnd={() => setIsTouchingChatArea(false)}
                >
                  <FlatList
                    data={filteredMessages}
                    renderItem={({ item: msg }) => {
                      if (msg.type === "system") {
                        return (
                          <View
                            key={msg._id}
                            style={styles.joinedMessageContainer}
                          >
                            <UserAvatar
                              image={getImageUrl(msg.user?.image)}
                              size={32}
                              username={msg.user?.username || ""}
                              color={msg.user?.color || "#2C2C2E"}
                            />
                            <View style={styles.joinedMessageContent}>
                              <Text style={styles.joinedUsername}>
                                {msg.user?.username || "Anonymous"}
                              </Text>
                              <Text style={styles.joinedText}>joined 👋</Text>
                            </View>
                          </View>
                        );
                      } else {
                        return (
                          <View key={msg._id} style={styles.chatMessageRow}>
                            <UserAvatar
                              image={getImageUrl(msg.user?.image)}
                              size={32}
                              username={msg.user?.username || ""}
                              color={msg.user?.color || "#2C2C2E"}
                            />
                            <View style={styles.chatMessageContent}>
                              <View style={styles.messageHeaderRow}>
                                <Text style={styles.chatMessageUsername}>
                                  {msg.user?.username || "Anonymous"}
                                </Text>
                                {stream && msg.userId === stream.hostId && (
                                  <View style={styles.hostBadge}>
                                    <Text style={styles.hostBadgeText}>
                                      Host
                                    </Text>
                                  </View>
                                )}
                                {stream && msg.userId !== stream.hostId &&
                                  isModerator(msg.userId) && (
                                    <View style={styles.modBadge}>
                                      <Text style={styles.modBadgeText}>
                                        Mod
                                      </Text>
                                    </View>
                                  )}
                              </View>
                              <Text style={styles.chatMessageText}>
                                {msg.type === "reaction" ? (
                                  <Text style={styles.reactionText}>
                                    {msg.text}
                                  </Text>
                                ) : (
                                  msg.text
                                )}
                              </Text>
                            </View>
                          </View>
                        );
                      }
                    }}
                    keyExtractor={(msg) => msg._id}
                    inverted
                    contentContainerStyle={styles.chatMessagesContent}
                    style={styles.chatScrollView}
                  />

                  <View style={styles.chatInputContainer}>
                    <TextInput
                      style={styles.chatInput}
                      placeholder="Say something..."
                      placeholderTextColor="#999"
                      value={message}
                      onChangeText={setMessage}
                      multiline={false}
                      onSubmitEditing={handleSendMessage}
                      returnKeyType="send"
                    />
                  </View>
                </View>
              </Animated.View>

              {!uiVisible && showSwipeIndicator && (
                <View style={styles.swipeIndicator}>
                  <Ionicons name="chevron-forward" size={24} color="#fff" />
                  <Text style={styles.swipeText}>
                    Swipe right to show controls
                  </Text>
                </View>
              )}
            </View>
          </SafeAreaView>
        </View>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    position: "relative",
  },
  safeArea: {
    flex: 1,
    position: "relative",
  },
  gestureContainer: {
    flex: 1,
    position: "relative",
    zIndex: 2,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%",
    zIndex: 0,
  },
  overlayGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(28, 28, 30, 0.5)",
    zIndex: 1,
    pointerEvents: "none", // Ensure the overlay doesn't block touches
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "transparent",
    paddingHorizontal: 0,
    justifyContent: "flex-start",
    zIndex: 2,
  },
  scheduledBanner: {
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    margin: 16,
    padding: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#3A3A3C",
  },
  bannerSubtitle: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 8,
    textAlign: "center",
  },
  bannerTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 16,
    textAlign: "center",
  },
  saveYellowButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
    marginBottom: 8,
  },
  saveYellowButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  shareButtonWhite: {
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
  },
  shareButtonWhiteText: {
    color: "#000",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  pollContainer: {
    backgroundColor: "rgba(40, 40, 40, 0.9)",
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  pollHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  pollQuestion: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  pollOption: {
    backgroundColor: "rgba(60, 60, 60, 0.8)",
    borderRadius: 25,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#5A5A5C",
  },
  pollOptionText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
  },
  chatMainContainer: {
    backgroundColor: "transparent",
    marginBottom: 8,
    height: Dimensions.get("window").height * 0.33,
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
  },
  chatScrollView: {
    marginBottom: 8,
  },
  chatMessagesContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    paddingTop: 8,
  },
  hostMessageContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  hostAvatarContainer: {
    marginRight: 8,
  },
  hostMessageContent: {
    flex: 1,
  },
  hostNameRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  messageHeaderRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  hostUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 8,
  },
  hostBadge: {
    backgroundColor: "#E93560",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  hostBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  modBadge: {
    backgroundColor: "#4A90E2",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  modBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  hostMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  joinedMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
  },
  joinedMessageContent: {
    marginLeft: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  joinedUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 4,
  },
  joinedText: {
    color: "#fff",
    fontSize: 15,
  },
  chatMessageRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  chatMessageContent: {
    flex: 1,
    marginLeft: 8,
  },
  chatMessageUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
  },
  chatMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  reactionText: {
    fontSize: 20,
  },
  productInfoContainer: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
  },
  productTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  productPrice: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "700",
    marginTop: 4,
  },
  chatInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 0.5,
    borderTopColor: "rgba(100, 100, 100, 0.3)",
    // backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  chatInput: {
    flex: 1,
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    color: "#fff",
    fontSize: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "rgba(100, 100, 100, 0.3)",
  },
  sendButton: {
    backgroundColor: "#007AFF",
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  sendButtonDisabled: {
    backgroundColor: "#333",
  },
  sendButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  streamName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "700",
  },
  liveIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  liveText: {
    color: "#fff",
    fontSize: 16,
    marginLeft: 6,
  },
  shareButton: {
    backgroundColor: "#1a96d2",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 10,
    width: "100%",
    marginTop: 8,
  },
  shareButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  explicitBanner: {
    backgroundColor: "#D7263D",
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    zIndex: 101,
    minWidth: 260,
    maxWidth: "80%",
    alignSelf: "center",
  },
  explicitBannerContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 101,
    pointerEvents: "box-none",
  },
  explicitBannerText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  topSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    backgroundColor: "transparent",
    zIndex: 10,
  },
  topLeftRow: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  userAvatarContainer: {
    marginRight: 8,
  },
  topUsername: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    maxWidth: 180,
  },
  topRightRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  chatUserRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  swipeIndicator: {
    position: "absolute",
    left: 20,
    top: "50%",
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  swipeText: {
    color: "#fff",
    marginLeft: 5,
    fontSize: 14,
  },
  startStreamButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingVertical: 16,
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
    marginBottom: 8,
  },
  startStreamButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    marginLeft: 8,
  },
});

export default StreamScreen;
